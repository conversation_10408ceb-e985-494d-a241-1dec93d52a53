import torch.nn as nn
#初始化神经网络
class simplecnn(nn.Module):  #定义一个nn.Module模型名为simplecnn的类
    def __init__(self,num_class): # num_class是我们的分类数
        super().__init__()
        self.features = nn.Sequential( # 做特征提取
            nn.Conv2d(3,16,kernel_size=3,stride=1,padding=1), # 保持图像大小不变 16* 224*224     卷积层 3通道是图片 代表RGB
            nn.ReLU(), # 卷积之后接上激活函数 增加非线特征                                        激活层
            nn.MaxPool2d(kernel_size=2,stride=2), # 池化之后变为 16*112*112                      池化层

            nn.Conv2d(16,32,kernel_size=3,stride=1,padding=1), # 保持图像大小不变 32* 112 *112   卷积层
            nn.ReLU(),                                                                         # 激活层
            nn.MaxPool2d(kernel_size=2,stride=2) # 图像大小变为 32*56*56                         池化层
        )
        # 定义全连接层 做分类
        self.classifier = nn.Sequential(
            nn.Linear(32*56*56,128), #                                                          全连接层
            nn.ReLU(),                                  #                                       激活层                                     
            nn.Linear(128,num_class) # num_class为分类的个数                                     全连接层
        )

    def forward(self,x):
        # 前向传播部分
        x = self.features(x) # 先将图像进行特征提取
        x = x.view(x.size(0),-1) # 展平 x.size(0) 为batch
        x = self.classifier(x)
        return x

# nn.Linear全连接层 nn.Conv2d卷积层 nn.ReLU激活函数
# Module类规定要定义__init__（初始化网络中的各个层）和forward（用于定义模型的前向传播逻辑）
#神经网络 输入层 隐藏层 输出层   隐藏层包括大类 卷积类 池化类 归一类 激活类 全连接类等随意拼接 直可以解决问题